import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { InputField, InputSchema } from '@/types/workflow';
import { AlertCircle } from 'lucide-react';

interface FormRendererProps {
  schema: InputSchema;
  values: Record<string, any>;
  onChange: (values: Record<string, any>) => void;
  errors?: Record<string, string>;
  onValidate?: (field: InputField, value: any) => string | null;
}

const FormRenderer: React.FC<FormRendererProps> = ({
  schema,
  values,
  onChange,
  errors = {},
  onValidate
}) => {
  const [touched, setTouched] = useState<Record<string, boolean>>({});

  const handleFieldChange = (fieldName: string, value: any) => {
    const newValues = { ...values, [fieldName]: value };
    onChange(newValues);
    
    // Mark field as touched
    setTouched(prev => ({ ...prev, [fieldName]: true }));
  };

  const validateField = (field: InputField, value: any): string | null => {
    // Custom validation function takes precedence
    if (onValidate) {
      const customError = onValidate(field, value);
      if (customError) return customError;
    }

    const validation = field.validation;
    if (!validation) return null;

    // Required validation
    if (validation.required && (value === undefined || value === null || value === '')) {
      return validation.customMessage || `${field.label} is required`;
    }

    // Skip other validations if field is empty and not required
    if (!validation.required && (value === undefined || value === null || value === '')) {
      return null;
    }

    // String/textarea validations
    if ((field.type === 'string' || field.type === 'textarea') && typeof value === 'string') {
      if (validation.minLength && value.length < validation.minLength) {
        return `${field.label} must be at least ${validation.minLength} characters`;
      }
      if (validation.maxLength && value.length > validation.maxLength) {
        return `${field.label} must be no more than ${validation.maxLength} characters`;
      }
    }

    // Number validations
    if (field.type === 'number' && typeof value === 'number') {
      if (validation.min !== undefined && value < validation.min) {
        return `${field.label} must be at least ${validation.min}`;
      }
      if (validation.max !== undefined && value > validation.max) {
        return `${field.label} must be no more than ${validation.max}`;
      }
    }

    // Email validation
    if (field.type === 'email' && typeof value === 'string') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        return `${field.label} must be a valid email address`;
      }
    }

    // URL validation
    if (field.type === 'url' && typeof value === 'string') {
      try {
        new URL(value);
      } catch {
        return `${field.label} must be a valid URL`;
      }
    }

    // Pattern validation
    if (validation.pattern && typeof value === 'string') {
      const regex = new RegExp(validation.pattern);
      if (!regex.test(value)) {
        return validation.customMessage || `${field.label} format is invalid`;
      }
    }

    return null;
  };

  const renderField = (field: InputField) => {
    const value = values[field.name];
    const error = errors[field.name] || (touched[field.name] ? validateField(field, value) : null);
    const hasError = !!error;

    const commonProps = {
      id: field.name,
      value: value || '',
      onChange: (newValue: any) => handleFieldChange(field.name, newValue),
      placeholder: field.placeholder,
      className: hasError ? 'border-red-500' : '',
    };

    const renderInput = () => {
      switch (field.type) {
        case 'string':
        case 'email':
        case 'url':
          return (
            <Input
              {...commonProps}
              type={field.type === 'email' ? 'email' : field.type === 'url' ? 'url' : 'text'}
              onChange={(e) => commonProps.onChange(e.target.value)}
            />
          );

        case 'textarea':
          return (
            <Textarea
              {...commonProps}
              onChange={(e) => commonProps.onChange(e.target.value)}
              rows={4}
            />
          );

        case 'number':
          return (
            <Input
              {...commonProps}
              type="number"
              onChange={(e) => commonProps.onChange(e.target.value ? parseFloat(e.target.value) : '')}
              min={field.validation?.min}
              max={field.validation?.max}
            />
          );

        case 'date':
          return (
            <Input
              {...commonProps}
              type="date"
              onChange={(e) => commonProps.onChange(e.target.value)}
            />
          );

        case 'boolean':
          return (
            <div className="flex items-center space-x-2">
              <Checkbox
                id={field.name}
                checked={!!value}
                onCheckedChange={(checked) => commonProps.onChange(checked)}
              />
              <Label htmlFor={field.name} className="text-sm font-normal">
                {field.label}
              </Label>
            </div>
          );

        case 'select':
          return (
            <Select value={value || ''} onValueChange={commonProps.onChange}>
              <SelectTrigger className={hasError ? 'border-red-500' : ''}>
                <SelectValue placeholder={field.placeholder || `Select ${field.label}`} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map((option) => (
                  <SelectItem key={option.value} value={String(option.value)}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );

        case 'multiselect':
          const selectedValues = Array.isArray(value) ? value : [];
          return (
            <div className="space-y-2">
              {field.options?.map((option) => (
                <div key={option.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${field.name}_${option.value}`}
                    checked={selectedValues.includes(option.value)}
                    onCheckedChange={(checked) => {
                      const newValues = checked
                        ? [...selectedValues, option.value]
                        : selectedValues.filter(v => v !== option.value);
                      commonProps.onChange(newValues);
                    }}
                  />
                  <Label htmlFor={`${field.name}_${option.value}`} className="text-sm font-normal">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          );

        case 'file':
          return (
            <Input
              id={field.name}
              type="file"
              onChange={(e) => {
                const file = e.target.files?.[0];
                commonProps.onChange(file);
              }}
              className={hasError ? 'border-red-500' : ''}
            />
          );

        default:
          return (
            <Input
              {...commonProps}
              onChange={(e) => commonProps.onChange(e.target.value)}
            />
          );
      }
    };

    return (
      <div key={field.id} className="space-y-2">
        {field.type !== 'boolean' && (
          <Label htmlFor={field.name} className="text-sm font-medium">
            {field.label}
            {field.validation?.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
        )}
        
        {renderInput()}
        
        {field.description && (
          <p className="text-xs text-neutral-500 dark:text-neutral-400">
            {field.description}
          </p>
        )}
        
        {error && (
          <Alert variant="destructive" className="py-2">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">{error}</AlertDescription>
          </Alert>
        )}
      </div>
    );
  };

  if (!schema.fields || schema.fields.length === 0) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-neutral-500">
            <p>No input fields defined in the schema.</p>
            <p className="text-sm mt-1">Configure the input node to add form fields.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{schema.title || 'Input Form'}</CardTitle>
        {schema.description && (
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            {schema.description}
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-6">
        {schema.fields.map(renderField)}
      </CardContent>
    </Card>
  );
};

export default FormRenderer;
