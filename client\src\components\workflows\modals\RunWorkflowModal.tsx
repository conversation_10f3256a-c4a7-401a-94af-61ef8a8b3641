import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Workflow, InputSchema } from '@/types/workflow';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';
import FormRenderer from '../FormRenderer';

interface RunWorkflowModalProps {
  workflow: Workflow;
  onClose: () => void;
  onRunComplete: () => void;
}

const RunWorkflowModal: React.FC<RunWorkflowModalProps> = ({ workflow, onClose, onRunComplete }) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState<Record<string, any>>({});

  // Find the input node and its schema
  const inputNode = Object.values(workflow.nodes).find(node => node.type === 'input');
  const rawSchema = inputNode?.data.schema || {};

  // Handle both new and legacy schema formats
  const isNewSchema = rawSchema && typeof rawSchema === 'object' && 'fields' in rawSchema;
  const schema: InputSchema = isNewSchema
    ? rawSchema as InputSchema
    : {
        version: "1.0",
        title: "Input Form",
        description: "Workflow input parameters",
        fields: Object.entries(rawSchema).map(([name, type], index) => ({
          id: `field_${index}`,
          name,
          label: name.charAt(0).toUpperCase() + name.slice(1),
          type: type as any,
          validation: { required: type === 'string' || type === 'number' }
        }))
      };

  // Validation function for form fields
  const validateForm = (): string[] => {
    const errors: string[] = [];

    schema.fields.forEach(field => {
      const value = formData[field.name];
      const isRequired = field.validation?.required;

      if (isRequired && (value === undefined || value === null || value === '')) {
        errors.push(field.label || field.name);
      }
    });

    return errors;
  };

  // Run workflow mutation
  const runWorkflowMutation = useMutation({
    mutationFn: async () => {
      // Validate required fields
      const missingFields = validateForm();

      if (missingFields.length > 0) {
        throw new Error(`Missing required fields: ${missingFields.join(', ')}`);
      }

      return apiRequest('POST', '/api/workflow-runs', {
        workflowId: workflow.id,
        status: 'running',
        triggerType: 'manual',
        input: formData
      });
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/workflow-runs?workflowId=${workflow.id}`] });
      toast({
        title: "Workflow running",
        description: "The workflow execution has started",
      });
      onRunComplete();
    },
    onError: (error) => {
      toast({
        title: "Failed to run workflow",
        description: error.message,
        variant: "destructive",
      });
    }
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    runWorkflowMutation.mutate();
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Run Workflow: {workflow.name}</DialogTitle>
          <DialogDescription>
            Provide the input data for this workflow run.
            {schema.fields.length === 0 && (
              <span className="text-yellow-600 dark:text-yellow-500 block mt-2">
                No input schema defined. Please configure the input node first.
              </span>
            )}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <FormRenderer
            schema={schema}
            values={formData}
            onChange={setFormData}
          />

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={runWorkflowMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={runWorkflowMutation.isPending || schema.fields.length === 0}
            >
              {runWorkflowMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Running...
                </>
              ) : (
                'Run Workflow'
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default RunWorkflowModal;