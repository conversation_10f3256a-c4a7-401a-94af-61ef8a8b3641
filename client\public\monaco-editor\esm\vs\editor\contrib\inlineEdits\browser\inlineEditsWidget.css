/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor div.inline-edits-widget {
	--widget-color: var(--vscode-notifications-background);

	.promptEditor .monaco-editor {
		--vscode-editor-placeholder-foreground: var(--vscode-editorGhostText-foreground);
	}

	.toolbar, .promptEditor {
		opacity: 0;
		transition: opacity 0.2s ease-in-out;
	}
	&:hover, &.focused {
		.toolbar, .promptEditor {
			opacity: 1;
		}
	}

	.preview .monaco-editor {

		.mtk1 {
			/*color: rgba(215, 215, 215, 0.452);*/
			color: var(--vscode-editorGhostText-foreground);
		}
		.view-overlays .current-line-exact {
			border: none;
		}

		.current-line-margin {
			border: none;
		}

		--vscode-editor-background: var(--widget-color);
	}

	svg {
		.gradient-start {
			stop-color: var(--vscode-editor-background);
		}

		.gradient-stop {
			stop-color: var(--widget-color);
		}
	}
}
