import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Trash2, GripVertical, Settings } from 'lucide-react';
import { InputField, InputFieldType, InputSchema, InputFieldValidation, InputFieldOption } from '@/types/workflow';

interface SchemaBuilderProps {
  schema: InputSchema;
  onChange: (schema: InputSchema) => void;
}

const FIELD_TYPES: { value: InputFieldType; label: string; description: string }[] = [
  { value: 'string', label: 'Text', description: 'Single line text input' },
  { value: 'textarea', label: 'Long Text', description: 'Multi-line text input' },
  { value: 'number', label: 'Number', description: 'Numeric input' },
  { value: 'email', label: 'Email', description: 'Email address input' },
  { value: 'url', label: 'URL', description: 'Website URL input' },
  { value: 'date', label: 'Date', description: 'Date picker' },
  { value: 'boolean', label: 'Yes/No', description: 'Checkbox or toggle' },
  { value: 'select', label: 'Dropdown', description: 'Single selection from options' },
  { value: 'multiselect', label: 'Multi-select', description: 'Multiple selections from options' },
  { value: 'file', label: 'File Upload', description: 'File upload input' },
];

const SchemaBuilder: React.FC<SchemaBuilderProps> = ({ schema, onChange }) => {
  const [selectedFieldId, setSelectedFieldId] = useState<string | null>(null);

  const addField = () => {
    const newField: InputField = {
      id: `field_${Date.now()}`,
      name: `field_${schema.fields.length + 1}`,
      label: `Field ${schema.fields.length + 1}`,
      type: 'string',
      validation: { required: false }
    };

    onChange({
      ...schema,
      fields: [...schema.fields, newField]
    });
  };

  const updateField = (fieldId: string, updates: Partial<InputField>) => {
    onChange({
      ...schema,
      fields: schema.fields.map(field => 
        field.id === fieldId ? { ...field, ...updates } : field
      )
    });
  };

  const removeField = (fieldId: string) => {
    onChange({
      ...schema,
      fields: schema.fields.filter(field => field.id !== fieldId)
    });
    if (selectedFieldId === fieldId) {
      setSelectedFieldId(null);
    }
  };

  const moveField = (fieldId: string, direction: 'up' | 'down') => {
    const currentIndex = schema.fields.findIndex(f => f.id === fieldId);
    if (currentIndex === -1) return;

    const newIndex = direction === 'up' ? currentIndex - 1 : currentIndex + 1;
    if (newIndex < 0 || newIndex >= schema.fields.length) return;

    const newFields = [...schema.fields];
    [newFields[currentIndex], newFields[newIndex]] = [newFields[newIndex], newFields[currentIndex]];

    onChange({
      ...schema,
      fields: newFields
    });
  };

  const updateValidation = (fieldId: string, validation: InputFieldValidation) => {
    updateField(fieldId, { validation });
  };

  const updateOptions = (fieldId: string, options: InputFieldOption[]) => {
    updateField(fieldId, { options });
  };

  const selectedField = selectedFieldId ? schema.fields.find(f => f.id === selectedFieldId) : null;

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-full">
      {/* Schema Overview */}
      <div className="lg:col-span-2 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Schema Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="schema-title">Schema Title</Label>
              <Input
                id="schema-title"
                value={schema.title || ''}
                onChange={(e) => onChange({ ...schema, title: e.target.value })}
                placeholder="Enter schema title"
              />
            </div>
            <div>
              <Label htmlFor="schema-description">Description</Label>
              <Textarea
                id="schema-description"
                value={schema.description || ''}
                onChange={(e) => onChange({ ...schema, description: e.target.value })}
                placeholder="Describe what this input form is for"
                rows={2}
              />
            </div>
          </CardContent>
        </Card>

        {/* Fields List */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle>Form Fields</CardTitle>
            <Button onClick={addField} size="sm">
              <Plus className="w-4 h-4 mr-2" />
              Add Field
            </Button>
          </CardHeader>
          <CardContent>
            {schema.fields.length === 0 ? (
              <div className="text-center py-8 text-neutral-500">
                No fields defined. Click "Add Field" to get started.
              </div>
            ) : (
              <div className="space-y-2">
                {schema.fields.map((field, index) => (
                  <div
                    key={field.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedFieldId === field.id 
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20' 
                        : 'border-neutral-200 dark:border-neutral-700 hover:border-neutral-300'
                    }`}
                    onClick={() => setSelectedFieldId(field.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <GripVertical className="w-4 h-4 text-neutral-400" />
                        <div>
                          <div className="font-medium">{field.label}</div>
                          <div className="text-sm text-neutral-500">
                            {field.name} • {FIELD_TYPES.find(t => t.value === field.type)?.label}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {field.validation?.required && (
                          <Badge variant="secondary" className="text-xs">Required</Badge>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            moveField(field.id, 'up');
                          }}
                          disabled={index === 0}
                        >
                          ↑
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            moveField(field.id, 'down');
                          }}
                          disabled={index === schema.fields.length - 1}
                        >
                          ↓
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            removeField(field.id);
                          }}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Field Configuration Panel */}
      <div className="space-y-4">
        {selectedField ? (
          <FieldConfigPanel
            field={selectedField}
            onUpdate={(updates) => updateField(selectedField.id, updates)}
            onUpdateValidation={(validation) => updateValidation(selectedField.id, validation)}
            onUpdateOptions={(options) => updateOptions(selectedField.id, options)}
          />
        ) : (
          <Card>
            <CardContent className="pt-6">
              <div className="text-center text-neutral-500">
                <Settings className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>Select a field to configure its properties</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
};

interface FieldConfigPanelProps {
  field: InputField;
  onUpdate: (updates: Partial<InputField>) => void;
  onUpdateValidation: (validation: InputFieldValidation) => void;
  onUpdateOptions: (options: InputFieldOption[]) => void;
}

const FieldConfigPanel: React.FC<FieldConfigPanelProps> = ({
  field,
  onUpdate,
  onUpdateValidation,
  onUpdateOptions
}) => {
  const [newOption, setNewOption] = useState({ label: '', value: '' });

  const addOption = () => {
    if (!newOption.label || !newOption.value) return;
    
    const options = field.options || [];
    onUpdateOptions([...options, { ...newOption }]);
    setNewOption({ label: '', value: '' });
  };

  const removeOption = (index: number) => {
    const options = field.options || [];
    onUpdateOptions(options.filter((_, i) => i !== index));
  };

  const needsOptions = field.type === 'select' || field.type === 'multiselect';

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Field Configuration</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Properties */}
        <div>
          <Label htmlFor="field-label">Label</Label>
          <Input
            id="field-label"
            value={field.label}
            onChange={(e) => onUpdate({ label: e.target.value })}
          />
        </div>

        <div>
          <Label htmlFor="field-name">Field Name</Label>
          <Input
            id="field-name"
            value={field.name}
            onChange={(e) => onUpdate({ name: e.target.value })}
            placeholder="field_name"
          />
        </div>

        <div>
          <Label htmlFor="field-type">Field Type</Label>
          <Select value={field.type} onValueChange={(value: InputFieldType) => onUpdate({ type: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {FIELD_TYPES.map(type => (
                <SelectItem key={type.value} value={type.value}>
                  <div>
                    <div className="font-medium">{type.label}</div>
                    <div className="text-xs text-neutral-500">{type.description}</div>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div>
          <Label htmlFor="field-description">Description</Label>
          <Textarea
            id="field-description"
            value={field.description || ''}
            onChange={(e) => onUpdate({ description: e.target.value })}
            placeholder="Help text for this field"
            rows={2}
          />
        </div>

        <div>
          <Label htmlFor="field-placeholder">Placeholder</Label>
          <Input
            id="field-placeholder"
            value={field.placeholder || ''}
            onChange={(e) => onUpdate({ placeholder: e.target.value })}
            placeholder="Placeholder text"
          />
        </div>

        {/* Validation */}
        <div className="space-y-3">
          <Label>Validation Rules</Label>
          
          <div className="flex items-center space-x-2">
            <Checkbox
              id="required"
              checked={field.validation?.required || false}
              onCheckedChange={(checked) => 
                onUpdateValidation({ ...field.validation, required: checked as boolean })
              }
            />
            <Label htmlFor="required">Required field</Label>
          </div>

          {(field.type === 'string' || field.type === 'textarea') && (
            <>
              <div>
                <Label htmlFor="min-length">Minimum Length</Label>
                <Input
                  id="min-length"
                  type="number"
                  value={field.validation?.minLength || ''}
                  onChange={(e) => 
                    onUpdateValidation({ 
                      ...field.validation, 
                      minLength: e.target.value ? parseInt(e.target.value) : undefined 
                    })
                  }
                />
              </div>
              <div>
                <Label htmlFor="max-length">Maximum Length</Label>
                <Input
                  id="max-length"
                  type="number"
                  value={field.validation?.maxLength || ''}
                  onChange={(e) => 
                    onUpdateValidation({ 
                      ...field.validation, 
                      maxLength: e.target.value ? parseInt(e.target.value) : undefined 
                    })
                  }
                />
              </div>
            </>
          )}

          {field.type === 'number' && (
            <>
              <div>
                <Label htmlFor="min-value">Minimum Value</Label>
                <Input
                  id="min-value"
                  type="number"
                  value={field.validation?.min || ''}
                  onChange={(e) => 
                    onUpdateValidation({ 
                      ...field.validation, 
                      min: e.target.value ? parseFloat(e.target.value) : undefined 
                    })
                  }
                />
              </div>
              <div>
                <Label htmlFor="max-value">Maximum Value</Label>
                <Input
                  id="max-value"
                  type="number"
                  value={field.validation?.max || ''}
                  onChange={(e) => 
                    onUpdateValidation({ 
                      ...field.validation, 
                      max: e.target.value ? parseFloat(e.target.value) : undefined 
                    })
                  }
                />
              </div>
            </>
          )}
        </div>

        {/* Options for select fields */}
        {needsOptions && (
          <div className="space-y-3">
            <Label>Options</Label>
            
            {field.options?.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <Input value={option.label} readOnly className="flex-1" />
                <Input value={option.value} readOnly className="w-24" />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeOption(index)}
                >
                  <Trash2 className="w-4 h-4" />
                </Button>
              </div>
            ))}

            <div className="flex items-center space-x-2">
              <Input
                placeholder="Option label"
                value={newOption.label}
                onChange={(e) => setNewOption({ ...newOption, label: e.target.value })}
                className="flex-1"
              />
              <Input
                placeholder="Value"
                value={newOption.value}
                onChange={(e) => setNewOption({ ...newOption, value: e.target.value })}
                className="w-24"
              />
              <Button onClick={addOption} size="sm">
                <Plus className="w-4 h-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SchemaBuilder;
