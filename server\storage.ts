import {
  users, type User, type InsertUser,
  workflows, type Workflow, type InsertWorkflow,
  credentials, type Credential, type InsertCredential,
  workflowRuns, type WorkflowRun, type InsertWorkflowRun,
  nodeRuns, type NodeRun, type InsertNodeRun
} from "@shared/schema";

// Storage interface for CRUD operations
export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;

  // Workflow operations
  getWorkflows(): Promise<Workflow[]>;
  getWorkflow(id: number): Promise<Workflow | undefined>;
  createWorkflow(workflow: InsertWorkflow): Promise<Workflow>;
  updateWorkflow(id: number, workflow: Partial<InsertWorkflow>): Promise<Workflow | undefined>;
  deleteWorkflow(id: number): Promise<boolean>;
  getFavoriteWorkflows(): Promise<Workflow[]>;
  toggleWorkflowFavorite(id: number): Promise<Workflow | undefined>;

  // Credential operations
  getCredentials(): Promise<Credential[]>;
  getCredential(id: number): Promise<Credential | undefined>;
  createCredential(credential: InsertCredential): Promise<Credential>;
  updateCredential(id: number, credential: Partial<InsertCredential>): Promise<Credential | undefined>;
  deleteCredential(id: number): Promise<boolean>;

  // Workflow run operations
  getWorkflowRuns(workflowId?: number): Promise<WorkflowRun[]>;
  getWorkflowRun(id: number): Promise<WorkflowRun | undefined>;
  createWorkflowRun(run: InsertWorkflowRun): Promise<WorkflowRun>;
  updateWorkflowRun(id: number, run: Partial<WorkflowRun>): Promise<WorkflowRun | undefined>;

  // Node run operations
  getNodeRuns(workflowRunId: number): Promise<NodeRun[]>;
  createNodeRun(run: InsertNodeRun): Promise<NodeRun>;
  updateNodeRun(id: number, run: Partial<NodeRun>): Promise<NodeRun | undefined>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private workflows: Map<number, Workflow>;
  private credentials: Map<number, Credential>;
  private workflowRuns: Map<number, WorkflowRun>;
  private nodeRuns: Map<number, NodeRun>;

  private currentUserId: number;
  private currentWorkflowId: number;
  private currentCredentialId: number;
  private currentWorkflowRunId: number;
  private currentNodeRunId: number;

  constructor() {
    this.users = new Map();
    this.workflows = new Map();
    this.credentials = new Map();
    this.workflowRuns = new Map();
    this.nodeRuns = new Map();

    this.currentUserId = 1;
    this.currentWorkflowId = 1;
    this.currentCredentialId = 1;
    this.currentWorkflowRunId = 1;
    this.currentNodeRunId = 1;

    // Create default user
    this.createUser({
      username: "developer",
      password: "password"
    });

    // Seed some sample data
    this.seedSampleData();
  }

  // User operations
  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentUserId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  // Workflow operations
  async getWorkflows(): Promise<Workflow[]> {
    return Array.from(this.workflows.values());
  }

  async getWorkflow(id: number): Promise<Workflow | undefined> {
    return this.workflows.get(id);
  }

  async createWorkflow(insertWorkflow: InsertWorkflow): Promise<Workflow> {
    const id = this.currentWorkflowId++;
    const now = new Date();
    const workflow: Workflow = {
      ...insertWorkflow,
      id,
      createdAt: now,
      updatedAt: now
    };
    this.workflows.set(id, workflow);
    return workflow;
  }

  async updateWorkflow(id: number, updateData: Partial<InsertWorkflow>): Promise<Workflow | undefined> {
    const workflow = this.workflows.get(id);
    if (!workflow) return undefined;

    const updatedWorkflow: Workflow = {
      ...workflow,
      ...updateData,
      updatedAt: new Date()
    };
    this.workflows.set(id, updatedWorkflow);
    return updatedWorkflow;
  }

  async deleteWorkflow(id: number): Promise<boolean> {
    return this.workflows.delete(id);
  }

  async getFavoriteWorkflows(): Promise<Workflow[]> {
    return Array.from(this.workflows.values()).filter(wf => wf.isFavorite);
  }

  async toggleWorkflowFavorite(id: number): Promise<Workflow | undefined> {
    const workflow = this.workflows.get(id);
    if (!workflow) return undefined;

    const updatedWorkflow: Workflow = {
      ...workflow,
      isFavorite: !workflow.isFavorite,
      updatedAt: new Date()
    };
    this.workflows.set(id, updatedWorkflow);
    return updatedWorkflow;
  }

  // Credential operations
  async getCredentials(): Promise<Credential[]> {
    return Array.from(this.credentials.values());
  }

  async getCredential(id: number): Promise<Credential | undefined> {
    return this.credentials.get(id);
  }

  async createCredential(insertCredential: InsertCredential): Promise<Credential> {
    const id = this.currentCredentialId++;
    const now = new Date();
    const credential: Credential = {
      ...insertCredential,
      id,
      createdAt: now
    };
    this.credentials.set(id, credential);
    return credential;
  }

  async updateCredential(id: number, updateData: Partial<InsertCredential>): Promise<Credential | undefined> {
    const credential = this.credentials.get(id);
    if (!credential) return undefined;

    const updatedCredential: Credential = {
      ...credential,
      ...updateData
    };
    this.credentials.set(id, updatedCredential);
    return updatedCredential;
  }

  async deleteCredential(id: number): Promise<boolean> {
    return this.credentials.delete(id);
  }

  // Workflow run operations
  async getWorkflowRuns(workflowId?: number): Promise<WorkflowRun[]> {
    const runs = Array.from(this.workflowRuns.values());
    return workflowId
      ? runs.filter(run => run.workflowId === workflowId)
      : runs;
  }

  async getWorkflowRun(id: number): Promise<WorkflowRun | undefined> {
    return this.workflowRuns.get(id);
  }

  async createWorkflowRun(insertRun: InsertWorkflowRun): Promise<WorkflowRun> {
    const id = this.currentWorkflowRunId++;
    const now = new Date();
    const run: WorkflowRun = {
      ...insertRun,
      id,
      startTime: now,
      endTime: null,
      logs: {}
    };
    this.workflowRuns.set(id, run);
    return run;
  }

  async updateWorkflowRun(id: number, updateData: Partial<WorkflowRun>): Promise<WorkflowRun | undefined> {
    const run = this.workflowRuns.get(id);
    if (!run) return undefined;

    const updatedRun: WorkflowRun = {
      ...run,
      ...updateData
    };
    this.workflowRuns.set(id, updatedRun);
    return updatedRun;
  }

  // Node run operations
  async getNodeRuns(workflowRunId: number): Promise<NodeRun[]> {
    return Array.from(this.nodeRuns.values())
      .filter(nodeRun => nodeRun.workflowRunId === workflowRunId);
  }

  async createNodeRun(insertRun: InsertNodeRun): Promise<NodeRun> {
    const id = this.currentNodeRunId++;
    const now = new Date();
    const run: NodeRun = {
      ...insertRun,
      id,
      startTime: now,
      endTime: null,
      error: null
    };
    this.nodeRuns.set(id, run);
    return run;
  }

  async updateNodeRun(id: number, updateData: Partial<NodeRun>): Promise<NodeRun | undefined> {
    const run = this.nodeRuns.get(id);
    if (!run) return undefined;

    const updatedRun: NodeRun = {
      ...run,
      ...updateData
    };
    this.nodeRuns.set(id, updatedRun);
    return updatedRun;
  }

  // Seed sample data for demonstration
  private seedSampleData() {
    // Sample credentials
    this.createCredential({
      name: "Google AI API Key",
      provider: "google",
      apiKey: "dummy-api-key-gemini"
    });

    this.createCredential({
      name: "OpenRouter API Key",
      provider: "openrouter",
      apiKey: "dummy-api-key-openrouter"
    });

    // Sample workflow
    const sampleWorkflow = this.createWorkflow({
      name: "Content Generator",
      description: "Generates content based on a topic",
      status: "published",
      isFavorite: true,
      nodes: {
        "input-1": {
          id: "input-1",
          type: "input",
          position: { x: 100, y: 120 },
          data: {
            name: "User Query Input",
            description: "Accepts user query for content generation",
            schema: {
              version: "1.0",
              title: "Content Generation Input",
              description: "Provide the topic for content generation",
              fields: [
                {
                  id: "field_1",
                  name: "query",
                  label: "Topic",
                  type: "string",
                  description: "The main topic or subject for content generation",
                  placeholder: "Enter the topic you want content about",
                  validation: {
                    required: true,
                    minLength: 3,
                    maxLength: 200
                  }
                },
                {
                  id: "field_2",
                  name: "tone",
                  label: "Writing Tone",
                  type: "select",
                  description: "The tone of voice for the generated content",
                  options: [
                    { label: "Professional", value: "professional" },
                    { label: "Casual", value: "casual" },
                    { label: "Academic", value: "academic" },
                    { label: "Creative", value: "creative" }
                  ],
                  validation: {
                    required: false
                  }
                },
                {
                  id: "field_3",
                  name: "length",
                  label: "Content Length",
                  type: "number",
                  description: "Approximate word count for the content",
                  placeholder: "500",
                  validation: {
                    required: false,
                    min: 100,
                    max: 5000
                  }
                }
              ]
            }
          }
        },
        "prompt-1": {
          id: "prompt-1",
          type: "prompt",
          position: { x: 420, y: 260 },
          data: {
            name: "Topic Expander",
            description: "Expands user query into detailed topic",
            prompt: "Create a detailed outline for an article about: {{query}}",
            model: "Gemini Pro",
            provider: "google",
            credentialId: 1,
            maxTokens: 1000,
            temperature: 0.7,
            outputFormat: "text"
          }
        },
        "agent-1": {
          id: "agent-1",
          type: "agent",
          position: { x: 720, y: 120 },
          data: {
            name: "Content Writer",
            description: "Writes full article from outline",
            systemPrompt: "You are a professional content writer. Create a well-structured article based on this outline.",
            model: "Claude 3 Opus",
            provider: "openrouter",
            credentialId: 2,
            outputFormat: "json",
            schema: { title: "string", content: "string", sections: ["string"] }
          }
        },
        "api-1": {
          id: "api-1",
          type: "api",
          position: { x: 1020, y: 260 },
          data: {
            name: "Content API",
            description: "Posts content to CMS",
            url: "https://api.cms.example/content",
            method: "POST",
            headers: { "Content-Type": "application/json" },
            authType: "apiKey",
            apiKeyHeader: "x-api-key",
            credentialId: null
          }
        }
      },
      edges: {
        "e1-2": {
          id: "e1-2",
          source: "input-1",
          target: "prompt-1"
        },
        "e2-3": {
          id: "e2-3",
          source: "prompt-1",
          target: "agent-1"
        },
        "e3-4": {
          id: "e3-4",
          source: "agent-1",
          target: "api-1"
        }
      }
    });

    // Create additional workflows
    this.createWorkflow({
      name: "Email Responder",
      description: "Auto-responds to customer emails",
      status: "draft",
      isFavorite: false,
      nodes: {},
      edges: {}
    });

    this.createWorkflow({
      name: "Data Processor",
      description: "Processes structured data inputs",
      status: "published",
      isFavorite: true,
      nodes: {},
      edges: {}
    });

    this.createWorkflow({
      name: "API Integration",
      description: "Connects to external API endpoints",
      status: "published",
      isFavorite: false,
      nodes: {},
      edges: {}
    });

    // Sample workflow runs
    const run = this.createWorkflowRun({
      workflowId: 1,
      status: "completed",
      triggerType: "manual",
      input: {
        query: "renewable energy technologies",
        tone: "professional",
        length: 1000
      }
    });

    // Update with completion time
    this.updateWorkflowRun(run.id, {
      endTime: new Date(new Date(run.startTime).getTime() + 4800)
    });

    // Node runs for the workflow run
    const inputNodeRun = this.createNodeRun({
      workflowRunId: run.id,
      nodeId: "input-1",
      status: "completed",
      input: {
        query: "renewable energy technologies",
        tone: "professional",
        length: 1000
      },
      output: {
        query: "renewable energy technologies",
        tone: "professional",
        length: 1000
      },
      error: null
    });
    this.updateNodeRun(inputNodeRun.id, {
      endTime: new Date(new Date(inputNodeRun.startTime).getTime() + 50)
    });

    const promptNodeRun = this.createNodeRun({
      workflowRunId: run.id,
      nodeId: "prompt-1",
      status: "completed",
      input: { query: "renewable energy technologies" },
      output: "# Detailed Outline: Renewable Energy Technologies\n\n## 1. Introduction\n- Brief overview of the global energy crisis and climate change\n- Importance of transitioning to renewable energy sources\n- Current state of renewable energy adoption worldwide\n- Thesis: Renewable energy technologies offer viable, sustainable alternatives to fossil fuels\n\n## 2. Main Points\n### 2.1 Solar Energy Technologies\n### 2.2 Wind Power Systems\n### 2.3 Hydroelectric Power\n### 2.4 Emerging Renewable Technologies\n\n## 3. Supporting Details\n[Truncated for brevity]",
      error: null
    });
    this.updateNodeRun(promptNodeRun.id, {
      endTime: new Date(new Date(promptNodeRun.startTime).getTime() + 2300)
    });

    const agentNodeRun = this.createNodeRun({
      workflowRunId: run.id,
      nodeId: "agent-1",
      status: "completed",
      input: "# Detailed Outline: Renewable Energy Technologies...",
      output: {
        title: "The Future of Energy: Advancements in Renewable Technologies",
        content: "Renewable energy technologies have evolved significantly over the past decade, becoming increasingly efficient and cost-effective. This article explores the current landscape of renewable energy solutions, their benefits, and their potential for addressing global energy challenges...",
        sections: [
          "Introduction to Renewable Energy",
          "Solar Power Technologies",
          "Wind Energy Systems",
          "Hydroelectric Power Innovations",
          "Emerging Technologies",
          "Economic Considerations",
          "Future Outlook"
        ]
      },
      error: null
    });
    this.updateNodeRun(agentNodeRun.id, {
      endTime: new Date(new Date(agentNodeRun.startTime).getTime() + 2500)
    });
  }
}

// Use DatabaseStorage instead of MemStorage for persistence
import { db } from './db';
import { eq, desc } from 'drizzle-orm';

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user || undefined;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user || undefined;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db
      .insert(users)
      .values(insertUser)
      .returning();
    return user;
  }

  async getWorkflows(): Promise<Workflow[]> {
    return await db.select().from(workflows);
  }

  async getWorkflow(id: number): Promise<Workflow | undefined> {
    const [workflow] = await db.select().from(workflows).where(eq(workflows.id, id));
    return workflow || undefined;
  }

  async createWorkflow(insertWorkflow: InsertWorkflow): Promise<Workflow> {
    const [workflow] = await db
      .insert(workflows)
      .values(insertWorkflow)
      .returning();
    return workflow;
  }

  async updateWorkflow(id: number, updateData: Partial<InsertWorkflow>): Promise<Workflow | undefined> {
    const [workflow] = await db
      .update(workflows)
      .set({
        ...updateData,
        updatedAt: new Date()
      })
      .where(eq(workflows.id, id))
      .returning();
    return workflow || undefined;
  }

  async deleteWorkflow(id: number): Promise<boolean> {
    const result = await db
      .delete(workflows)
      .where(eq(workflows.id, id));
    return true;
  }

  async getFavoriteWorkflows(): Promise<Workflow[]> {
    return await db
      .select()
      .from(workflows)
      .where(eq(workflows.isFavorite, true));
  }

  async toggleWorkflowFavorite(id: number): Promise<Workflow | undefined> {
    const workflow = await this.getWorkflow(id);
    if (!workflow) return undefined;

    const [updatedWorkflow] = await db
      .update(workflows)
      .set({
        isFavorite: !workflow.isFavorite,
        updatedAt: new Date()
      })
      .where(eq(workflows.id, id))
      .returning();
    return updatedWorkflow || undefined;
  }

  async getCredentials(): Promise<Credential[]> {
    return await db.select().from(credentials);
  }

  async getCredential(id: number): Promise<Credential | undefined> {
    const [credential] = await db.select().from(credentials).where(eq(credentials.id, id));
    return credential || undefined;
  }

  async createCredential(insertCredential: InsertCredential): Promise<Credential> {
    const [credential] = await db
      .insert(credentials)
      .values(insertCredential)
      .returning();
    return credential;
  }

  async updateCredential(id: number, updateData: Partial<InsertCredential>): Promise<Credential | undefined> {
    const [credential] = await db
      .update(credentials)
      .set(updateData)
      .where(eq(credentials.id, id))
      .returning();
    return credential || undefined;
  }

  async deleteCredential(id: number): Promise<boolean> {
    await db
      .delete(credentials)
      .where(eq(credentials.id, id));
    return true;
  }

  async getWorkflowRuns(workflowId?: number): Promise<WorkflowRun[]> {
    if (workflowId) {
      return await db
        .select()
        .from(workflowRuns)
        .where(eq(workflowRuns.workflowId, workflowId))
        .orderBy(desc(workflowRuns.startTime));
    }

    return await db
      .select()
      .from(workflowRuns)
      .orderBy(desc(workflowRuns.startTime));
  }

  async getWorkflowRun(id: number): Promise<WorkflowRun | undefined> {
    const [run] = await db.select().from(workflowRuns).where(eq(workflowRuns.id, id));
    return run || undefined;
  }

  async createWorkflowRun(insertRun: InsertWorkflowRun): Promise<WorkflowRun> {
    const [run] = await db
      .insert(workflowRuns)
      .values({
        ...insertRun,
        input: insertRun.input || {},
        logs: {}
      })
      .returning();
    return run;
  }

  async updateWorkflowRun(id: number, updateData: Partial<WorkflowRun>): Promise<WorkflowRun | undefined> {
    const [run] = await db
      .update(workflowRuns)
      .set(updateData)
      .where(eq(workflowRuns.id, id))
      .returning();
    return run || undefined;
  }

  async getNodeRuns(workflowRunId: number): Promise<NodeRun[]> {
    return await db
      .select()
      .from(nodeRuns)
      .where(eq(nodeRuns.workflowRunId, workflowRunId));
  }

  async createNodeRun(insertRun: InsertNodeRun): Promise<NodeRun> {
    const [run] = await db
      .insert(nodeRuns)
      .values({
        ...insertRun,
        input: insertRun.input || {},
        output: insertRun.output || {}
      })
      .returning();
    return run;
  }

  async updateNodeRun(id: number, updateData: Partial<NodeRun>): Promise<NodeRun | undefined> {
    const [run] = await db
      .update(nodeRuns)
      .set(updateData)
      .where(eq(nodeRuns.id, id))
      .returning();
    return run || undefined;
  }
}

export const storage = new DatabaseStorage();
