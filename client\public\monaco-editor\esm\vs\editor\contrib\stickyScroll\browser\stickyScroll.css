/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.monaco-editor .sticky-widget {
	overflow: hidden;
}

.monaco-editor .sticky-widget-line-numbers {
	float: left;
	background-color: inherit;
}

.monaco-editor .sticky-widget-lines-scrollable {
	display: inline-block;
	position: absolute;
	overflow: hidden;
	width: var(--vscode-editorStickyScroll-scrollableWidth);
	background-color: inherit;
}

.monaco-editor .sticky-widget-lines {
	position: absolute;
	background-color: inherit;
}

.monaco-editor .sticky-line-number, .monaco-editor .sticky-line-content {
	color: var(--vscode-editorLineNumber-foreground);
	white-space: nowrap;
	display: inline-block;
	position: absolute;
	background-color: inherit;
}

.monaco-editor .sticky-line-number .codicon-folding-expanded,
.monaco-editor .sticky-line-number .codicon-folding-collapsed {
	float: right;
	transition: var(--vscode-editorStickyScroll-foldingOpacityTransition);
}

.monaco-editor .sticky-line-content {
	width: var(--vscode-editorStickyScroll-scrollableWidth);
	background-color: inherit;
	white-space: nowrap;
}

.monaco-editor .sticky-line-number-inner {
	display: inline-block;
	text-align: right;
}

.monaco-editor .sticky-widget {
	border-bottom: 1px solid var(--vscode-editorStickyScroll-border);
}

.monaco-editor .sticky-line-content:hover {
	background-color: var(--vscode-editorStickyScrollHover-background);
	cursor: pointer;
}

.monaco-editor .sticky-widget {
	width: 100%;
	box-shadow: var(--vscode-editorStickyScroll-shadow) 0 4px 2px -2px;
	z-index: 4;
	background-color: var(--vscode-editorStickyScroll-background);
	right: initial !important;
}

.monaco-editor .sticky-widget.peek {
	background-color: var(--vscode-peekViewEditorStickyScroll-background);
}
